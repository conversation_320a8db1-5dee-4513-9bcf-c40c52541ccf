# Enhanced init_floorplan Command Implementation

## Overview

This document describes the implementation of five new `TclDoubleOption` parameters for the `init_floorplan` TCL command in `src/interface/tcl/tcl_ifp/tcl_init_ifp.cpp`. These parameters enable automatic calculation of die and core areas when explicit areas are not provided.

## New Parameters Added

### 1. `core_util` - Core Utilization Factor
- **Type**: `TclDoubleOption`
- **Default**: No default value (must be specified for automatic calculation)
- **Description**: Core utilization factor (e.g., 0.7 for 70% utilization)

### 2. `cell_area` - Total Cell Area  
- **Type**: `TclDoubleOption`
- **Default**: No default value (must be specified for automatic calculation)
- **Description**: Total area of all cells in the design

### 3. `x_margin` - Horizontal Margin
- **Type**: `TclDoubleOption` 
- **Default**: 10.0
- **Description**: Margin for both horizontal sides from core to die

### 4. `y_margin` - Vertical Margin
- **Type**: `TclDoubleOption`
- **Default**: 10.0  
- **Description**: Margin for both vertical sides from core to die

### 5. `xy_ratio` - Width-to-Height Ratio
- **Type**: `TclDoubleOption`
- **Default**: 1.0
- **Description**: Width-to-height aspect ratio (1.0 = square, 2.0 = width twice height)

## Implementation Details

### Constructor Changes
```cpp
TclFpInit::TclFpInit(const char* cmd_name) : TclCmd(cmd_name)
{
  auto* core_util = new TclDoubleOption("-core_util", 0);
  auto* cell_area = new TclDoubleOption("-cell_area", 0);
  auto* x_margin = new TclDoubleOption("-x_margin", 0, 10.0);
  auto* y_margin = new TclDoubleOption("-y_margin", 0, 10.0);
  auto* xy_ratio = new TclDoubleOption("-xy_ratio", 0, 1.0);
  // ... existing options ...
  addOption(core_util);
  addOption(cell_area);
  addOption(x_margin);
  addOption(y_margin);
  addOption(xy_ratio);
  // ... add existing options ...
}
```

### Validation Logic
The `check()` method now validates that either:
- Explicit areas are provided (`-die_area` AND `-core_area`), OR
- Automatic calculation parameters are provided (`-core_util` AND `-cell_area`)

### Automatic Calculation Algorithm
When explicit areas are not provided, the system calculates:

1. **Total Core Area**: `total_core_area = cell_area / core_util`
2. **Core Dimensions**: 
   - `core_height = sqrt(total_core_area / xy_ratio)`
   - `core_width = total_core_area / core_height`
3. **Die Dimensions**:
   - `die_width = core_width + 2 * x_margin`
   - `die_height = core_height + 2 * y_margin`
4. **Final Areas**:
   - `die_area = [0.0, 0.0, die_width, die_height]`
   - `core_area = [x_margin, y_margin, x_margin + core_width, y_margin + core_height]`

## Usage Examples

### Original Explicit Method (Still Supported)
```tcl
init_floorplan \
   -die_area "0.0 0.0 149.96 150.128" \
   -core_area "9.996 10.08 139.964 140.048" \
   -core_site unit \
   -io_site unit \
   -corner_site unit
```

### New Automatic Calculation Method
```tcl
init_floorplan \
   -cell_area 10000 \
   -core_util 0.7 \
   -x_margin 15.0 \
   -y_margin 15.0 \
   -xy_ratio 1.0 \
   -core_site unit \
   -io_site unit \
   -corner_site unit
```

### Using Default Values
```tcl
init_floorplan \
   -cell_area 8000 \
   -core_util 0.6 \
   -core_site unit
   # x_margin, y_margin default to 10.0
   # xy_ratio defaults to 1.0
```

## Files Modified

1. **`src/interface/tcl/tcl_ifp/tcl_init_ifp.cpp`**:
   - Added new `TclDoubleOption` parameters in constructor
   - Modified `check()` method for validation
   - Enhanced `exec()` method with automatic calculation logic
   - Added `#include <cmath>` for sqrt function

## Testing

A test script `test_init_floorplan.tcl` has been created to verify:
1. Original explicit area specification still works
2. New automatic calculation functionality works correctly
3. Different aspect ratios work properly
4. Default values are applied correctly

## Backward Compatibility

The implementation maintains full backward compatibility. Existing scripts using explicit `-die_area` and `-core_area` parameters will continue to work without modification.

## Benefits

1. **Simplified Usage**: Users can specify design constraints (cell area, utilization) rather than calculating areas manually
2. **Automatic Optimization**: The system calculates optimal die and core dimensions based on design requirements
3. **Flexible Aspect Ratios**: Support for different width-to-height ratios
4. **Configurable Margins**: Customizable margins between core and die boundaries
5. **Backward Compatible**: Existing workflows remain unchanged
