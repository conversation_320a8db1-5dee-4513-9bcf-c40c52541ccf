#!/usr/bin/env tclsh

# Test script for the enhanced init_floorplan command with new TclDoubleOption parameters
# This script tests both the original explicit area specification and the new automatic calculation

puts "Testing enhanced init_floorplan command..."

# Test 1: Original explicit area specification (should still work)
puts "\n=== Test 1: Explicit area specification ==="
puts "Testing original functionality with explicit die_area and core_area..."

set DIE_AREA "0.0 0.0 149.96 150.128"
set CORE_AREA "9.996 10.08 139.964 140.048"
set PLACE_SITE "unit"
set IO_SITE "unit"
set CORNER_SITE "unit"

# This should work as before
init_floorplan \
   -die_area $DIE_AREA \
   -core_area $CORE_AREA \
   -core_site $PLACE_SITE \
   -io_site $IO_SITE \
   -corner_site $CORNER_SITE

puts "Test 1 completed successfully!"

# Test 2: New automatic calculation using the new parameters
puts "\n=== Test 2: Automatic area calculation ==="
puts "Testing new functionality with automatic die/core area calculation..."

# Example parameters for automatic calculation:
# - cell_area: 10000 (total area of all cells)
# - core_util: 0.7 (70% utilization)
# - x_margin: 15.0 (margin on left and right sides)
# - y_margin: 15.0 (margin on top and bottom sides)  
# - xy_ratio: 1.0 (square aspect ratio)

# Expected calculation:
# total_core_area = 10000 / 0.7 = 14285.7
# core_height = sqrt(14285.7 / 1.0) = 119.5
# core_width = 14285.7 / 119.5 = 119.5
# die_width = 119.5 + 2 * 15.0 = 149.5
# die_height = 119.5 + 2 * 15.0 = 149.5
# die_area = [0.0, 0.0, 149.5, 149.5]
# core_area = [15.0, 15.0, 134.5, 134.5]

init_floorplan \
   -cell_area 10000 \
   -core_util 0.7 \
   -x_margin 15.0 \
   -y_margin 15.0 \
   -xy_ratio 1.0 \
   -core_site $PLACE_SITE \
   -io_site $IO_SITE \
   -corner_site $CORNER_SITE

puts "Test 2 completed successfully!"

# Test 3: Test with different aspect ratio
puts "\n=== Test 3: Automatic calculation with different aspect ratio ==="
puts "Testing with xy_ratio = 2.0 (width twice the height)..."

# Expected calculation with xy_ratio = 2.0:
# total_core_area = 10000 / 0.8 = 12500
# core_height = sqrt(12500 / 2.0) = 79.06
# core_width = 12500 / 79.06 = 158.11
# die_width = 158.11 + 2 * 10.0 = 178.11
# die_height = 79.06 + 2 * 10.0 = 99.06

init_floorplan \
   -cell_area 10000 \
   -core_util 0.8 \
   -x_margin 10.0 \
   -y_margin 10.0 \
   -xy_ratio 2.0 \
   -core_site $PLACE_SITE \
   -io_site $IO_SITE \
   -corner_site $CORNER_SITE

puts "Test 3 completed successfully!"

# Test 4: Test default values for optional parameters
puts "\n=== Test 4: Testing default values ==="
puts "Testing with only required parameters (cell_area, core_util, core_site)..."
puts "Should use default values: x_margin=10.0, y_margin=10.0, xy_ratio=1.0"

init_floorplan \
   -cell_area 8000 \
   -core_util 0.6 \
   -core_site $PLACE_SITE

puts "Test 4 completed successfully!"

# Test 5: Test -use_fixed_size switch option
puts "\n=== Test 5: Testing -use_fixed_size switch ==="
puts "Testing -use_fixed_size to force explicit area specification..."

# This should work - explicit areas with -use_fixed_size
init_floorplan \
   -use_fixed_size \
   -die_area "0.0 0.0 200.0 200.0" \
   -core_area "20.0 20.0 180.0 180.0" \
   -core_site $PLACE_SITE

puts "Test 5 completed successfully!"

# Test 6: Test that -use_fixed_size disables automatic calculation
puts "\n=== Test 6: Verify -use_fixed_size disables auto calculation ==="
puts "This test should demonstrate that when -use_fixed_size is used,"
puts "the automatic calculation parameters are ignored and explicit areas are required."

# This should work - -use_fixed_size with explicit areas (auto calc params ignored)
init_floorplan \
   -use_fixed_size \
   -die_area "0.0 0.0 180.0 180.0" \
   -core_area "15.0 15.0 165.0 165.0" \
   -cell_area 5000 \
   -core_util 0.5 \
   -core_site $PLACE_SITE

puts "Test 6 completed successfully!"
puts "Note: The -cell_area and -core_util parameters were ignored due to -use_fixed_size"

# Test 7: Test automatic cell area retrieval from netlist
puts "\n=== Test 7: Automatic cell area retrieval ==="
puts "Testing automatic calculation with cell area retrieved from netlist..."
puts "Only -core_util is required; -cell_area will be automatically retrieved from dmInst->netlistInstArea()"

# This should work - automatic cell area retrieval
init_floorplan \
   -core_util 0.75 \
   -x_margin 12.0 \
   -y_margin 12.0 \
   -xy_ratio 1.5 \
   -core_site $PLACE_SITE

puts "Test 7 completed successfully!"
puts "Note: Cell area was automatically retrieved from the netlist instances"

# Test 8: Test that explicit cell_area still works when provided
puts "\n=== Test 8: Explicit cell_area override ==="
puts "Testing that explicit -cell_area still works when provided..."

# This should work - explicit cell area overrides automatic retrieval
init_floorplan \
   -core_util 0.65 \
   -cell_area 12000 \
   -x_margin 8.0 \
   -y_margin 8.0 \
   -xy_ratio 0.8 \
   -core_site $PLACE_SITE

puts "Test 8 completed successfully!"
puts "Note: Explicit -cell_area value was used instead of automatic retrieval"

puts "\n=== All tests completed successfully! ==="
puts "The enhanced init_floorplan command with optional -cell_area is working correctly."
